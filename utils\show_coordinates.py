#!/usr/bin/env python3
"""
Simple Coordinate Display Tool

This utility continuously displays the current mouse position coordinates.
Press Ctrl+C to exit.
"""

import pyautogui
import time
import os

def show_coordinates():
    """Continuously show the current mouse coordinates."""
    try:
        print("🖱️ Mouse Coordinate Monitor")
        print("==========================")
        print("This tool shows your current mouse position in real-time.")
        print("Move your cursor over the captcha image to see coordinates.")
        print("Press Ctrl+C to exit.")
        print("\nCoordinates:")
        
        while True:
            x, y = pyautogui.position()
            position_str = f"X: {x} Y: {y}"
            
            # Clear the line and print the current position
            print(position_str, end='\r')
            time.sleep(0.1)
            
    except KeyboardInterrupt:
        print("\n\nExiting coordinate display.")
        print("Use these coordinates to adjust your captcha capture area in auto_clicker.py")

if __name__ == "__main__":
    show_coordinates()
