"""
Test script to verify the success message detection
"""

from detect_success_message import detect_success_combined
import os
import time

def test_detection_with_sample():
    """Test the detection with a sample screenshot"""
    
    # List of screenshots to test with
    test_files = [
        "temp_settings_check.png", 
        "temp_settings_check_error.png",
        # Add more test files here if needed
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n\nTesting with {test_file}:")
            success = detect_success_combined(test_file)
            print(f"Detection result: {'✅ SUCCESS' if success else '❌ FAILED'}")
        else:
            print(f"File not found: {test_file}")

if __name__ == "__main__":
    print("🧪 Testing Success Message Detection")
    print("=" * 60)
    
    # Test with sample files
    test_detection_with_sample()
    
    print("\n\n🔍 Now testing with live screen capture")
    print("Please ensure the success message is visible on screen")
    for i in range(3, 0, -1):
        print(f"Capturing in {i}...")
        time.sleep(1)
    
    # Test with live capture
    detect_success_combined()
