import os




file_input = "uncleaned_accounts_login.txt"
clean_output = "accounts_login.txt"
clean_list = []



with open(file_input, "r") as f:
    accounts = f.readlines()
    for account in accounts:
        email,password,birth = account.split(";")
        clean_list.append(f"{email};{password}")

with open(clean_output,"a") as f:
    for account in clean_list:
        f.write(f"{account}\n")
        