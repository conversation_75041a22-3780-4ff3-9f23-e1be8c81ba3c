"""
Auto Clicker using PyAutoGUI
A reusable script for automating mouse clicks based on image detection.
"""

import pyautogui
import time
import os
import requests
import base64
from typing import Optional, Tuple


class ImageClicker:
    """A class for automating mouse clicks based on image detection."""
    
    def __init__(self, confidence: float = 0.8, wait_time: int = 5):
        """
        Initialize the ImageClicker.
        
        Args:
            confidence (float): Confidence level for image matching (0.0 to 1.0)
            wait_time (int): Default wait time before searching for images
        """
        self.confidence = confidence
        self.wait_time = wait_time
        # Disable pyautogui fail-safe for smoother operation
        pyautogui.FAILSAFE = True
        
    def wait_and_click_image(self, image_path: str, wait_time: Optional[int] = None, 
                           click_delay: float = 0.1) -> bool:
        """
        Wait for specified time, then locate and click an image on screen.
        
        Args:
            image_path (str): Path to the image file to search for
            wait_time (int, optional): Time to wait before searching (uses default if None)
            click_delay (float): Delay between mouse move and click
            
        Returns:
            bool: True if image was found and clicked, False otherwise
        """
        if wait_time is None:
            wait_time = self.wait_time
            
        print(f"Waiting {wait_time} seconds before searching for image...")
        time.sleep(wait_time)
        
        return self.click_image(image_path, click_delay)
    
    def click_image(self, image_path: str, click_delay: float = 0.1) -> bool:
        """
        Locate and click an image on screen immediately.
        
        Args:
            image_path (str): Path to the image file to search for
            click_delay (float): Delay between mouse move and click
            
        Returns:
            bool: True if image was found and clicked, False otherwise
        """
        if not os.path.exists(image_path):
            print(f"Error: Image file '{image_path}' not found.")
            return False
            
        print(f"Searching for image: {image_path}")
        
        try:
            location = pyautogui.locateCenterOnScreen(image_path, confidence=self.confidence)
            
            if location is not None:
                print(f"Image found at location: {location}")
                pyautogui.moveTo(location)
                time.sleep(click_delay)
                pyautogui.click()
                print("Image clicked successfully!")
                return True
            else:
                print("Image not found on the screen.")
                return False
                
        except Exception as e:
            print(f"Error occurred while searching for image: {e}")
            return False
    
    def wait_for_image(self, image_path: str, timeout: int = 30, 
                      check_interval: float = 1.0) -> Optional[Tuple[int, int]]:
        """
        Wait for an image to appear on screen within a timeout period.
        
        Args:
            image_path (str): Path to the image file to search for
            timeout (int): Maximum time to wait in seconds
            check_interval (float): Time between checks in seconds
            
        Returns:
            Tuple[int, int] or None: Location coordinates if found, None if timeout
        """
        if not os.path.exists(image_path):
            print(f"Error: Image file '{image_path}' not found.")
            return None
            
        print(f"Waiting for image to appear: {image_path} (timeout: {timeout}s)")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                location = pyautogui.locateCenterOnScreen(image_path, confidence=self.confidence)
                if location is not None:
                    print(f"Image appeared at location: {location}")
                    return location
            except Exception as e:
                print(f"Error while waiting for image: {e}")
                break
                
            time.sleep(check_interval)
        
        print(f"Timeout: Image not found within {timeout} seconds.")
        return None
    
    def click_multiple_images(self, image_paths: list, delay_between_clicks: float = 1.0) -> int:
        """
        Click multiple images in sequence.
        
        Args:
            image_paths (list): List of image file paths to click
            delay_between_clicks (float): Delay between each click
            
        Returns:
            int: Number of images successfully clicked
        """
        successful_clicks = 0
        
        for i, image_path in enumerate(image_paths):
            print(f"\nProcessing image {i+1}/{len(image_paths)}: {image_path}")
            
            if self.click_image(image_path):
                successful_clicks += 1
                if i < len(image_paths) - 1:  # Don't delay after the last click
                    time.sleep(delay_between_clicks)
            else:
                print(f"Failed to click image: {image_path}")
        
        print(f"\nSummary: Successfully clicked {successful_clicks}/{len(image_paths)} images.")
        return successful_clicks
    
    def set_confidence(self, confidence: float):
        """Set the confidence level for image matching."""
        if 0.0 <= confidence <= 1.0:
            self.confidence = confidence
            print(f"Confidence level set to: {confidence}")
        else:
            print("Error: Confidence must be between 0.0 and 1.0")
    
    def get_screen_size(self) -> Tuple[int, int]:
        """Get the current screen size."""
        return pyautogui.size()
    
    def take_screenshot(self, filename: str = "screenshot.png"):
        """Take a screenshot and save it to file."""
        screenshot = pyautogui.screenshot()
        screenshot.save(filename)
        print(f"Screenshot saved as: {filename}")
    
    def capture_captcha_area(self, x: int, y: int, width: int, height: int, filename: str = "captcha.png") -> str:
        """
        Capture a specific area of the screen (for captcha).
        
        Args:
            x (int): X coordinate of the top-left corner
            y (int): Y coordinate of the top-left corner
            width (int): Width of the area to capture
            height (int): Height of the area to capture
            filename (str): Name of the file to save the captcha image
            
        Returns:
            str: Path to the saved captcha image
        """
        screenshot = pyautogui.screenshot(region=(x, y, width, height))
        screenshot.save(filename)
        print(f"Captcha image saved as: {filename}")
        return filename
    
    def solve_captcha_with_solvecaptcha(self, image_path: str, api_key: str, 
                                      numeric: int = 0, phrase: int = 0, 
                                      regsense: int = 0, language: int = 2) -> Optional[str]:
        """
        Solve captcha using SolveCaptcha service.
        
        Args:
            image_path (str): Path to the captcha image file
            api_key (str): Your SolveCaptcha API key
            numeric (int): Type of captcha (0=not specified, 1=numbers only, 2=letters only, 3=numbers OR letters, 4=numbers AND letters)
            phrase (int): 0=one word, 1=two or more words
            regsense (int): 0=not case sensitive, 1=case sensitive
            language (int): 0=not specified, 1=Cyrillic, 2=Latin
            
        Returns:
            str or None: The solved captcha text, or None if failed
        """
        if not os.path.exists(image_path):
            print(f"Error: Captcha image file '{image_path}' not found.")
            return None
            
        print(f"Solving captcha: {image_path}")
        
        try:
            # Step 1: Submit captcha to SolveCaptcha
            with open(image_path, 'rb') as f:
                files = {'file': f}
                data = {
                    'key': api_key,
                    'method': 'post',
                    'numeric': numeric,
                    'phrase': phrase,
                    'regsense': regsense,
                    'language': language
                }
                
                response = requests.post('http://api.solvecaptcha.com/in.php', 
                                       files=files, data=data, timeout=30)
                
            if response.status_code != 200:
                print(f"Error submitting captcha: HTTP {response.status_code}")
                return None
                
            result = response.text.strip()
            print(f"Submit response: {result}")
            
            if not result.startswith('OK|'):
                print(f"Error submitting captcha: {result}")
                return None
                
            captcha_id = result.split('|')[1]
            print(f"Captcha ID: {captcha_id}")
            
            # Step 2: Wait and get the result
            print("Waiting for captcha to be solved...")
            max_attempts = 24  # 2 minutes maximum (24 * 5 seconds)
            
            for attempt in range(max_attempts):
                time.sleep(5)  # Wait 5 seconds before checking
                
                try:
                    result_response = requests.get(
                        'http://api.solvecaptcha.com/res.php',
                        params={
                            'key': api_key,
                            'action': 'get',
                            'id': captcha_id
                        },
                        timeout=30
                    )
                    
                    if result_response.status_code != 200:
                        print(f"Error getting result: HTTP {result_response.status_code}")
                        continue
                        
                    result_text = result_response.text.strip()
                    print(f"Attempt {attempt + 1}: {result_text}")
                    
                    if result_text.startswith('OK|'):
                        captcha_solution = result_text.split('|')[1]
                        print(f"✅ Captcha solved: {captcha_solution}")
                        return captcha_solution
                    elif result_text == 'CAPCHA_NOT_READY':
                        print("Captcha not ready yet, waiting...")
                        continue
                    else:
                        print(f"Error getting captcha result: {result_text}")
                        return None
                        
                except requests.RequestException as e:
                    print(f"Network error while getting result: {e}")
                    continue
            
            print("Timeout: Captcha was not solved within the time limit")
            return None
            
        except requests.RequestException as e:
            print(f"Network error while submitting captcha: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error solving captcha: {e}")
            return None
    
    def solve_captcha_base64(self, image_path: str, api_key: str, 
                           numeric: int = 0, phrase: int = 0, 
                           regsense: int = 0, language: int = 2) -> Optional[str]:
        """
        Solve captcha using SolveCaptcha service with base64 encoding.
        
        Args:
            image_path (str): Path to the captcha image file
            api_key (str): Your SolveCaptcha API key
            numeric (int): Type of captcha (0=not specified, 1=numbers only, 2=letters only, 3=numbers OR letters, 4=numbers AND letters)
            phrase (int): 0=one word, 1=two or more words
            regsense (int): 0=not case sensitive, 1=case sensitive
            language (int): 0=not specified, 1=Cyrillic, 2=Latin
            
        Returns:
            str or None: The solved captcha text, or None if failed
        """
        if not os.path.exists(image_path):
            print(f"Error: Captcha image file '{image_path}' not found.")
            return None
            
        print(f"Solving captcha with base64: {image_path}")
        
        try:
            # Encode image to base64
            with open(image_path, 'rb') as f:
                image_data = f.read()
                base64_data = base64.b64encode(image_data).decode('utf-8')
            
            # Step 1: Submit captcha to SolveCaptcha
            data = {
                'key': api_key,
                'method': 'base64',
                'body': base64_data,
                'numeric': numeric,
                'phrase': phrase,
                'regsense': regsense,
                'language': language
            }
            
            response = requests.post('http://api.solvecaptcha.com/in.php', 
                                   data=data, timeout=30)
            
            if response.status_code != 200:
                print(f"Error submitting captcha: HTTP {response.status_code}")
                return None
                
            result = response.text.strip()
            print(f"Submit response: {result}")
            
            if not result.startswith('OK|'):
                print(f"Error submitting captcha: {result}")
                return None
                
            captcha_id = result.split('|')[1]
            print(f"Captcha ID: {captcha_id}")
            
            # Step 2: Wait and get the result
            print("Waiting for captcha to be solved...")
            max_attempts = 24  # 2 minutes maximum
            
            for attempt in range(max_attempts):
                time.sleep(5)  # Wait 5 seconds before checking
                
                try:
                    result_response = requests.get(
                        'http://api.solvecaptcha.com/res.php',
                        params={
                            'key': api_key,
                            'action': 'get',
                            'id': captcha_id
                        },
                        timeout=30
                    )
                    
                    if result_response.status_code != 200:
                        print(f"Error getting result: HTTP {result_response.status_code}")
                        continue
                        
                    result_text = result_response.text.strip()
                    print(f"Attempt {attempt + 1}: {result_text}")
                    
                    if result_text.startswith('OK|'):
                        captcha_solution = result_text.split('|')[1]
                        print(f"✅ Captcha solved: {captcha_solution}")
                        return captcha_solution
                    elif result_text == 'CAPCHA_NOT_READY':
                        print("Captcha not ready yet, waiting...")
                        continue
                    else:
                        print(f"Error getting captcha result: {result_text}")
                        return None
                        
                except requests.RequestException as e:
                    print(f"Network error while getting result: {e}")
                    continue
            
            print("Timeout: Captcha was not solved within the time limit")
            return None
            
        except Exception as e:
            print(f"Error solving captcha: {e}")
            return None


# Convenience functions for quick use
def quick_click(image_path: str, confidence: float = 0.8, wait_time: int = 5) -> bool:
    """
    Quick function to click an image with default settings.
    
    Args:
        image_path (str): Path to the image file to search for
        confidence (float): Confidence level for image matching
        wait_time (int): Time to wait before searching
        
    Returns:
        bool: True if image was found and clicked, False otherwise
    """
    clicker = ImageClicker(confidence=confidence, wait_time=wait_time)
    return clicker.wait_and_click_image(image_path)


def click_now(image_path: str, confidence: float = 0.8) -> bool:
    """
    Quick function to click an image immediately without waiting.
    
    Args:
        image_path (str): Path to the image file to search for
        confidence (float): Confidence level for image matching
        
    Returns:
        bool: True if image was found and clicked, False otherwise
    """
    clicker = ImageClicker(confidence=confidence)
    return clicker.click_image(image_path)


if __name__ == "__main__":
    # Example usage
    print("PyAutoGUI Auto Clicker with Captcha Solving")
    print("=" * 45)
    
    # Create an instance of ImageClicker
    clicker = ImageClicker(confidence=0.8, wait_time=5)

    # Workflow example
    clicker.wait_and_click_image("screens/settings_icon.png")
    clicker.wait_and_click_image("screens/pop3.png")
    clicker.wait_and_click_image("screens/enable_pop3.png")
    clicker.wait_and_click_image("screens/save.png")

    # Captcha handling
    location = clicker.wait_for_image("screens/captcha_dialog.png", timeout=10)
    if location:
        print("Captcha dialog detected!")
        
        # Example API key (replace with your actual key)
        API_KEY = "YOUR_SOLVECAPTCHA_API_KEY_HERE"
        
        # Option 1: Capture a specific area where the captcha image is located
        # You need to adjust these coordinates based on where the captcha appears
        captcha_x = location[0] - 100  # Adjust these coordinates
        captcha_y = location[1] - 50   # based on your captcha position
        captcha_width = 200            # relative to the dialog location
        captcha_height = 80
        
        captcha_image = clicker.capture_captcha_area(captcha_x, captcha_y, 
                                                   captcha_width, captcha_height, 
                                                   "current_captcha.png")
        
        # Solve the captcha
        solution = clicker.solve_captcha_with_solvecaptcha(
            captcha_image, 
            API_KEY,
            numeric=0,    # 0=not specified, 1=numbers only, 2=letters only, 4=numbers and letters
            phrase=0,     # 0=one word, 1=multiple words
            language=2    # 2=Latin characters
        )
        
        if solution:
            print(f"🎉 Captcha solution: {solution}")
            
            # Now you can type the solution or click on the input field and type
            # Find the captcha input field and type the solution
            input_location = clicker.wait_for_image("screens/captcha_input.png", timeout=5)
            if input_location:
                pyautogui.click(input_location)
                time.sleep(0.5)
                pyautogui.typewrite(solution)
                print(f"Typed captcha solution: {solution}")
            else:
                print("Captcha input field not found, you'll need to type manually:")
                print(f"Solution: {solution}")
        else:
            print("❌ Failed to solve captcha")
    else:
        print("No captcha dialog detected")

    print("\nScript completed! See captcha_solver_example.py for more examples.")
