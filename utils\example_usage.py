"""
Example usage of the Auto Clicker script.
This demonstrates various ways to use the ImageClicker class.
"""

from auto_clicker_bb import ImageClicker, quick_click, click_now
import time

def main():
    print("Auto Clicker Examples")
    print("=" * 40)
    
    # Method 1: Using the ImageClicker class
    print("\n1. Using ImageClicker class:")
    clicker = ImageClicker(confidence=0.8, wait_time=3)
    
    # Example: Click a button after waiting 3 seconds
    # Place your target image in the same folder and update the filename
    image_file = "target_button.png"  # Change this to your image file
    
    print(f"Looking for image: {image_file}")
    print("Make sure to place your target image in the same folder!")
    
    # Uncomment the line below to actually perform the click
    # result = clicker.wait_and_click_image(image_file)
    # print(f"Click result: {result}")
    
    print("\n2. Using convenience functions:")
    # Quick click with 5 second wait
    # quick_click("button.png", confidence=0.9, wait_time=5)
    
    # Immediate click without waiting
    # click_now("instant_button.png", confidence=0.8)
    
    print("\n3. Clicking multiple images in sequence:")
    # List of images to click in order
    images_to_click = [
        "first_button.png",
        "second_button.png", 
        "third_button.png"
    ]
    
    # Uncomment to click all images with 2 second delays between clicks
    # clicker.click_multiple_images(images_to_click, delay_between_clicks=2.0)
    
    print("\n4. Wait for image to appear:")
    # Wait up to 10 seconds for an image to appear on screen
    # location = clicker.wait_for_image("popup_button.png", timeout=10)
    # if location:
    #     print(f"Image found at: {location}")
    #     # Could click it here if needed
    
    print("\n5. Taking a screenshot:")
    # Take a screenshot for reference
    clicker.take_screenshot("current_screen.png")
    
    print("\nTo use this script:")
    print("1. Install requirements: pip install -r requirements.txt")
    print("2. Take screenshots of buttons/elements you want to click")
    print("3. Save them as PNG files in this folder")
    print("4. Update the image filenames in this script")
    print("5. Uncomment the relevant lines and run!")

if __name__ == "__main__":
    main()
