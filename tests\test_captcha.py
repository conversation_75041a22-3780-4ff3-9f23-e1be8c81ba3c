from auto_clicker_bb import ImageClicker
import os

def test_captcha_functionality():
    """Test the captcha solving setup without actual API calls."""
    
    print("🔧 Testing Captcha Functionality")
    print("=" * 40)
    
    # Create clicker instance
    clicker = ImageClicker(confidence=0.8)
    
    # Test 1: Check if we can create a test captcha image
    print("\n1. Testing screenshot capability...")
    clicker.take_screenshot("test_screenshot.png")
    
    if os.path.exists("test_screenshot.png"):
        print("✅ Screenshot functionality working")
        
        # Test 2: Test captcha area capture
        print("\n2. Testing captcha area capture...")
        test_captcha = clicker.capture_captcha_area(100, 100, 200, 80, "test_captcha.png")
        
        if os.path.exists(test_captcha):
            print("✅ Captcha area capture working")
            
            # Test 3: Check captcha solving function (without API call)
            print("\n3. Testing captcha solving function structure...")
            
            # This won't actually solve since we don't have a real API key
            # but it will test the function structure
            fake_api_key = "6d471eb76317bea61bc7fc56bc18d1e6"
            
            try:
                # This should fail gracefully due to invalid API key
                result = clicker.solve_captcha_with_solvecaptcha(
                    test_captcha, 
                    fake_api_key,
                    numeric=0,
                    language=2
                )
                
                # Expected to be None due to invalid API key
                if result is None:
                    print("✅ Captcha solving function structure working (returned None as expected)")
                else:
                    print(f"⚠️ Unexpected result: {result}")
                    
            except Exception as e:
                print(f"⚠️ Function structure test failed: {e}")
            
            print("\n🎯 Setup Complete!")
            print("=" * 40)
            print("Your captcha solving setup is ready!")
            print("\nTo use with real captchas:")
            print("1. Get your API key from: https://solvecaptcha.com/")
            print("2. Replace 'YOUR_SOLVECAPTCHA_API_KEY_HERE' in the scripts")
            print("3. Take screenshots of actual captcha dialogs")
            print("4. Update the image paths in your scripts")
            print("5. Run your automation workflow!")
            
            print(f"\nTest files created:")
            print(f"- test_screenshot.png")
            print(f"- test_captcha.png")
            
        else:
            print("❌ Captcha area capture failed")
    else:
        print("❌ Screenshot functionality failed")

def show_usage_example():
    """Show a usage example for real captcha solving."""
    
    print("\n" + "="*50)
    print("EXAMPLE USAGE FOR REAL CAPTCHA SOLVING")
    print("="*50)
    
    example_code = '''
# Your actual usage would look like this:

from auto_clicker import ImageClicker
import pyautogui

# Set your real API key
API_KEY = "your_real_api_key_here"

# Create clicker
clicker = ImageClicker()

# Wait for captcha to appear
location = clicker.wait_for_image("screens/captcha_dialog.png", timeout=10)

if location:
    print("Captcha detected!")
    
    # Capture the captcha area (adjust coordinates for your specific captcha)
    captcha_image = clicker.capture_captcha_area(
        location[0] - 100,  # X offset from dialog
        location[1] - 50,   # Y offset from dialog  
        200,                # Width
        80,                 # Height
        "current_captcha.png"
    )
    
    # Solve it
    solution = clicker.solve_captcha_with_solvecaptcha(
        captcha_image, 
        API_KEY,
        numeric=0,    # Adjust based on your captcha type
        language=2    # 2 = Latin characters
    )
    
    if solution:
        print(f"Solution: {solution}")
        
        # Type the solution
        input_location = clicker.wait_for_image("screens/captcha_input.png")
        if input_location:
            pyautogui.click(input_location)
            pyautogui.typewrite(solution)
    '''
    
    print(example_code)

if __name__ == "__main__":
    test_captcha_functionality()
    show_usage_example()
