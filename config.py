import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


# API Keys
TWOCAPTCHA_API_KEY = "d315b270071ccc3922a75b7c56e72da1"
SMS_ACTIVATE_API_KEY = "4c6521A3229dd7e3d92cd9AA0e53f5f4"



GMX_REGISTRATION_URL = "http://signup.gmx.com/"


# Timeouts (in milliseconds)
PAGE_TIMEOUT = 100000
NAVIGATION_TIMEOUT = 50000
CAPTCHA_TIMEOUT = 120000

# File paths
OUTPUT_FILE = "created_accounts.txt"
LOG_FILE = "gmx_creator.log"

# Proxy settings (optional)
USE_PROXY = False
PROXY_HOST = ""
PROXY_PORT = ""
PROXY_USERNAME = ""
PROXY_PASSWORD = ""

# API URLs
OPENPLZ_API_URL = "https://www.openplzapi.org/api/v1"
FAKEXY_URL = "https://www.fakexy.com/fake-address-generator-de"
FUNGENERATORS_URL = "https://fungenerators.com/random/name/german"
RANDOMUSER_API_URL = "https://randomuser.me/api?nat=gb"
SMS_ACTIVATE_API_URL = "https://api.sms-activate.org/stubs/handler_api.php"

# Registration settings
PASSWORD_LENGTH = 14  # Longer passwords are more secure and more likely to pass GMX's requirements

# SMS verification settings
SMS_COUNTRY_CODE = 16  # SMS Activate code: 43=Germany(+49), 50=Austria(+43), 41=Switzerland(+41)
SMS_SERVICE_CODE = "abk"  # Service code for GMX on sms-activate.io
SMS_TIMEOUT = 600  # Maximum time to wait for SMS code in seconds
SMS_CHECK_INTERVAL = 5  # Interval between SMS status checks in seconds
SMS_MAX_RETRIES = 3  # Maximum number of retries for SMS verification
SMS_PHONE_ROTATION = True  # Whether to rotate phone numbers
SMS_MAX_PHONES = 3  # Maximum number of phone numbers to keep in rotation (2-3 recommended for $2 balance)
SMS_RENTAL_PERIOD = 14400  # Phone number rental period in seconds (4 hours = 14400 seconds)
