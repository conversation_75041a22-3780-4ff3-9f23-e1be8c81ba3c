# PyAutoGUI Auto Clicker

A reusable Python script for automating mouse clicks based on image detection using PyAutoGUI.

## Features

- **Image-based clicking**: Click on any element by providing a screenshot
- **Flexible timing**: Configurable wait times and delays
- **Multiple click modes**: Single click, multiple clicks, wait for image to appear
- **Captcha solving**: Automatic captcha solving using SolveCaptcha service
- **Error handling**: Robust error handling and logging
- **Easy to use**: Simple functions for quick automation tasks

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

Or install dependencies individually:
```bash
pip install pyautogui pillow requests
```

## Quick Start

### Method 1: Using the ImageClicker Class

```python
from auto_clicker import ImageClicker

# Create clicker instance
clicker = ImageClicker(confidence=0.8, wait_time=5)

# Click an image after waiting 5 seconds
clicker.wait_and_click_image("button.png")

# Click an image immediately
clicker.click_image("target.png")
```

### Method 2: Using Convenience Functions

```python
from auto_clicker import quick_click, click_now

# Quick click with default settings
quick_click("my_button.png")

# Click immediately without waiting
click_now("instant_button.png")
```

## Advanced Usage

### Click Multiple Images in Sequence

```python
clicker = ImageClicker()
images = ["step1.png", "step2.png", "step3.png"]
clicker.click_multiple_images(images, delay_between_clicks=2.0)
```

### Wait for Image to Appear

```python
# Wait up to 30 seconds for an image to appear
location = clicker.wait_for_image("popup.png", timeout=30)
if location:
    print(f"Image found at: {location}")
```

### Take Screenshots

```python
# Take a screenshot for creating target images
clicker.take_screenshot("reference.png")
```

## Captcha Solving

The script includes integration with SolveCaptcha service for automatic captcha solving.

### Setup

1. Get your API key from [SolveCaptcha](https://solvecaptcha.com/)
2. Replace `YOUR_SOLVECAPTCHA_API_KEY_HERE` with your actual API key

### Basic Captcha Solving

```python
# Solve a captcha image
solution = clicker.solve_captcha_with_solvecaptcha(
    "captcha.png",           # Path to captcha image
    "your_api_key_here",     # Your SolveCaptcha API key
    numeric=0,               # 0=any, 1=numbers only, 2=letters only, 4=numbers and letters
    phrase=0,                # 0=one word, 1=multiple words
    language=2               # 2=Latin characters
)

if solution:
    print(f"Captcha solved: {solution}")
```

### Automated Captcha Handling

```python
# Wait for captcha dialog to appear
location = clicker.wait_for_image("captcha_dialog.png", timeout=10)

if location:
    # Capture the captcha area
    captcha_image = clicker.capture_captcha_area(
        location[0] - 100, location[1] - 50,  # Adjust coordinates
        200, 80,                              # Width, height
        "current_captcha.png"
    )
    
    # Solve it
    solution = clicker.solve_captcha_with_solvecaptcha(
        captcha_image, "your_api_key"
    )
    
    if solution:
        # Find input field and type solution
        input_location = clicker.wait_for_image("captcha_input.png")
        if input_location:
            pyautogui.click(input_location)
            pyautogui.typewrite(solution)
```

### Captcha Methods

- **`solve_captcha_with_solvecaptcha()`** - Solve using multipart file upload
- **`solve_captcha_base64()`** - Solve using base64 encoding
- **`capture_captcha_area()`** - Capture specific screen area for captcha

## Preparing Target Images

1. Take a screenshot of the element you want to click
2. Crop the image to show just the button/element
3. Save as PNG format
4. Ensure the image matches exactly what appears on screen
5. Place the image file in your project folder

## Configuration Options

- **confidence**: Image matching confidence (0.0 to 1.0, default: 0.8)
- **wait_time**: Default wait time before searching (seconds, default: 5)
- **click_delay**: Delay between mouse move and click (seconds, default: 0.1)

## Safety Features

- PyAutoGUI fail-safe is enabled (move mouse to top-left corner to stop)
- Error handling for missing images and search failures
- Timeout protection for waiting functions

## Examples

See `example_usage.py` for basic examples and `captcha_solver_example.py` for captcha solving demonstrations.

## Tips

- Use higher confidence (0.9+) for better accuracy
- Take screenshots at the same resolution you'll run the script
- Ensure target images are clear and distinct
- Test with different confidence levels if clicks are unreliable
- Use the wait functions for applications that need time to load

## Troubleshooting

- **Image not found**: Check image path and ensure exact visual match
- **Wrong clicks**: Adjust confidence level or retake screenshot
- **Script won't stop**: Move mouse to top-left corner (fail-safe)
- **Import errors**: Ensure PyAutoGUI and Pillow are installed

## File Structure

```
PythonClicker/
├── auto_clicker.py              # Main script with ImageClicker class
├── example_usage.py             # Basic usage examples
├── captcha_solver_example.py    # Captcha solving examples
├── requirements.txt             # Python dependencies
└── README.md                   # This file
```
