"""
Captcha handling module for the Auto Clicker
"""

import pyautogui
import time
import os

def handle_captcha_and_save_settings(clicker, max_attempts=3):
    """
    Reusable function to handle captcha detection, solving, and verification.
    Will retry if the captcha solution is incorrect.
    
    Args:
        clicker: Instance of the ImageClicker class
        max_attempts (int): Maximum number of attempts to solve the captcha
        
    Returns:
        bool: True if successfully completed, False otherwise
    """
    location = clicker.wait_for_captcha("screens/captcha_dialog.png", timeout=30)
    if not location:
        print("No captcha dialog detected")
        return False
        
    print("Captcha dialog detected!")
    
    time.sleep(3)
    
    # API key for captcha solving service
    API_KEY = "6d471eb76317bea61bc7fc56bc18d1e6"
    
    # Pre-defined captcha area coordinates
    captcha_x = 1096
    captcha_y = 743
    captcha_width = 300          
    captcha_height = 80
    
    for attempt in range(max_attempts):
        print(f"\n🔄 Captcha solving attempt {attempt + 1}/{max_attempts}")
        time.sleep(1)  # Brief pause before starting
        
        # Capture the current captcha
        captcha_image = clicker.capture_captcha_area(
            captcha_x, captcha_y, 
            captcha_width, captcha_height, 
            "current_captcha.png"
        )
        
        # Solve the captcha using the official package
        solution = clicker.solve_captcha_with_solvecaptcha(
            captcha_image, 
            API_KEY,
            numeric=0,
            phrase=0,
            lang=0
        )
        
        if not solution:
            print(f"❌ Failed to solve captcha on attempt {attempt + 1}")
            continue
            
        print(f"🎉 Captcha solution: {solution}")
        
        # Find and click the input field
        input_location = clicker.wait_for_image("screens/captcha_input.png", timeout=15)
        if not input_location:
            print("Captcha input field not found")
            continue
            
        print(f"📍 Input field found at: {input_location}")
        
        # Click and focus the input field
        pyautogui.click(input_location)
        time.sleep(0.5)
        
        # Test if field is focused
        test_char = 'x'
        pyautogui.typewrite(test_char)
        time.sleep(0.2)
        pyautogui.press('backspace')
        time.sleep(0.3)
        
        # Clear any existing text thoroughly
        print("🧹 Clearing existing text...")
        pyautogui.hotkey('ctrl', 'a')
        time.sleep(0.2)
        pyautogui.press('delete')
        time.sleep(0.3)
        pyautogui.hotkey('ctrl', 'a')  # Double clear
        time.sleep(0.2)
        pyautogui.press('delete')
        time.sleep(0.5)
        
        # Type the captcha solution
        print(f"Attempting to type solution: '{solution}'")
        try:
            for i, char in enumerate(solution['code']):
                print(f"  Typing character {i+1}/{len(solution['code'])}: '{char}'")
                pyautogui.typewrite(char)
                time.sleep(0.2)
            print("✅ Typing completed")
            time.sleep(0.5)
        except Exception as e:
            print(f"❌ Typing failed: {e}")
            continue
            
        # Click the continue button
        clicker.wait_and_click_image("screens/captcha_dialog.png", wait_time=3)
        
        # Take a screenshot to check results
        print("Waiting 0.5 seconds then taking a screenshot to check results...")
        time.sleep(0.5)
        temp_screenshot = "temp_settings_check.png"
        pyautogui.screenshot().save(temp_screenshot)
        
        # Check if code was incorrect
        try:
            code_incorrect = pyautogui.locate("screens/code_incorrect.png", temp_screenshot, confidence=0.7)
            if code_incorrect:
                print("❌ Captcha code was incorrect. Retrying...")
                # Clean up the temporary screenshot
                if os.path.exists(temp_screenshot):
                    os.remove(temp_screenshot)
                continue  # Try again with a new captcha solution
                
            # Check if settings were saved successfully
            settings_saved = pyautogui.locate("screens/settings_saved.png", temp_screenshot, confidence=0.7)
            if settings_saved:
                print("✅ Settings were successfully saved!")
                print("LOGOUT")
                clicker.wait_and_click_image("screens/logout.png", wait_time=5)
                # Clean up the temporary screenshot
                if os.path.exists(temp_screenshot):
                    os.remove(temp_screenshot)
                return True  # Success! No need for further attempts
            else:
                print("⚠️ Settings saved confirmation not detected in the screenshot")
                print("Checking for code_incorrect...")
        except Exception as e:
            print(f"Error checking screenshot: {e}")
        
        # Clean up the temporary screenshot
        try:
            if os.path.exists(temp_screenshot):
                os.remove(temp_screenshot)
        except Exception as e:
            print(f"Error removing temporary screenshot: {e}")
    
    # If we get here, we've exhausted all attempts
    print(f"⚠️ All {max_attempts} captcha solving attempts failed")
    print("Attempting to logout anyway...")
    clicker.wait_and_click_image("screens/logout.png", wait_time=5)
    return False
