#!/usr/bin/env python3
"""
Coordinate Recorder Tool

This utility helps you record exact screen coordinates by moving your mouse
and pressing Enter to mark positions. Perfect for finding the exact location
and dimensions of UI elements like captcha images.
"""

import pyautogui
import keyboard
import time
import os
from datetime import datetime

def record_coordinates():
    """Record mouse coordinates when Enter key is pressed."""
    points = []
    
    print("==== Coordinate Recorder Tool ====")
    print("Move your cursor to each corner of the captcha image")
    print("and press ENTER to record each position.")
    print("Record all 4 corners in order: Top-Left, Top-Right, Bottom-Right, Bottom-Left")
    print("Press ESC at any time to exit.")
    print("\nWaiting for you to position the cursor...")
    
    while len(points) < 4:
        # Wait for Enter key press
        if keyboard.is_pressed('enter'):
            # Get current position
            pos = pyautogui.position()
            points.append(pos)
            print(f"Point {len(points)} recorded: {pos}")
            
            # Take a screenshot with the point marked
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Ensure screenshots directory exists
            if not os.path.exists("coord_screenshots"):
                os.makedirs("coord_screenshots")
            
            screenshot_path = os.path.join("coord_screenshots", f"point_{len(points)}_{timestamp}.png")
            screenshot = pyautogui.screenshot()
            screenshot.save(screenshot_path)
            
            # Wait until key is released to avoid multiple records
            while keyboard.is_pressed('enter'):
                time.sleep(0.1)
            
            print("Move to the next corner...")
            time.sleep(0.5)  # Prevent accidental double-recording
        
        # Check for escape to exit
        if keyboard.is_pressed('esc'):
            print("Recording canceled.")
            return None
        
        time.sleep(0.1)  # Reduce CPU usage
    
    # Calculate captcha dimensions from the four points
    top_left = points[0]
    top_right = points[1]
    bottom_right = points[2]
    bottom_left = points[3]
    
    # Calculate width and height
    width = max(abs(top_right.x - top_left.x), abs(bottom_right.x - bottom_left.x))
    height = max(abs(bottom_left.y - top_left.y), abs(bottom_right.y - top_right.y))
    
    # Calculate center point
    center_x = int((top_left.x + top_right.x + bottom_right.x + bottom_left.x) / 4)
    center_y = int((top_left.y + top_right.y + bottom_right.y + bottom_left.y) / 4)
    
    # Calculate top-left corner
    x = int(top_left.x)
    y = int(top_left.y)
    
    print("\nResults:")
    print(f"Top-Left: {top_left}")
    print(f"Top-Right: {top_right}")
    print(f"Bottom-Right: {bottom_right}")
    print(f"Bottom-Left: {bottom_left}")
    print("\nCaptcha Dimensions:")
    print(f"Width: {width} pixels")
    print(f"Height: {height} pixels")
    print(f"Center Point: ({center_x}, {center_y})")
    
    print("\nFor capture_captcha_area method:")
    print(f"captcha_x = {x}")
    print(f"captcha_y = {y}")
    print(f"captcha_width = {width}")
    print(f"captcha_height = {height}")
    
    print("\nFor dialog-centered calculation:")
    dialog_x_offset = center_x - int(location[0])  # Replace location[0] with your dialog's x coordinate
    dialog_y_offset = center_y - int(location[1])  # Replace location[1] with your dialog's y coordinate
    print(f"captcha_x = int(dialog_x {'-' if dialog_x_offset >= 0 else '+'} {abs(dialog_x_offset)})")
    print(f"captcha_y = int(dialog_y {'+' if dialog_y_offset >= 0 else '-'} {abs(dialog_y_offset)})")
    
    # Take a final screenshot with the calculated area highlighted
    take_verification_screenshot(x, y, width, height)
    
    return {
        'points': points,
        'x': x,
        'y': y,
        'width': width,
        'height': height,
        'center_x': center_x,
        'center_y': center_y
    }

def take_verification_screenshot(x, y, width, height):
    """Take a screenshot with the calculated area highlighted."""
    import cv2
    import numpy as np
    
    # Ensure screenshots directory exists
    if not os.path.exists("coord_screenshots"):
        os.makedirs("coord_screenshots")
    
    # Take a screenshot
    screenshot = pyautogui.screenshot()
    screenshot_np = np.array(screenshot)
    
    # Convert RGB to BGR (for OpenCV)
    screenshot_np = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
    
    # Draw rectangle around the captcha area
    cv2.rectangle(
        screenshot_np, 
        (x, y), 
        (x + width, y + height), 
        (0, 255, 0),  # Green color
        2  # Line thickness
    )
    
    # Add text label
    cv2.putText(
        screenshot_np,
        f"Captcha Area: {width}x{height}",
        (x, y - 10),
        cv2.FONT_HERSHEY_SIMPLEX,
        0.7,
        (0, 255, 0),
        2
    )
    
    # Save the image
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_path = os.path.join("coord_screenshots", f"captcha_area_{timestamp}.png")
    cv2.imwrite(result_path, screenshot_np)
    
    print(f"\nVerification screenshot saved: {result_path}")
    print("Green rectangle shows the calculated captcha area")

def update_auto_clicker_code(coords):
    """Generate code to update the auto_clicker.py file."""
    if not coords:
        return
    
    x = coords['x']
    y = coords['y']
    width = coords['width']
    height = coords['height']
    center_x = coords['center_x']
    center_y = coords['center_y']
    
    print("\n==== Code to Update auto_clicker.py ====")
    print("Replace the captcha area calculation code with:")
    print("""
        # Calculate captcha image coordinates based on recorded values
        dialog_x = int(location[0])
        dialog_y = int(location[1])
        
        # Captcha dimensions from coordinate recording tool
        captcha_width = {}
        captcha_height = {}
        
        # Calculate position relative to dialog location
        captcha_x = int(dialog_x + ({} - dialog_x))  # Absolute X position
        captcha_y = int(dialog_y + ({} - dialog_y))  # Absolute Y position
    """.format(width, height, x, y))
    
    print("\nOr use absolute coordinates (not recommended):")
    print("""
        # Use absolute coordinates for captcha (not recommended)
        captcha_x = {}
        captcha_y = {}
        captcha_width = {}
        captcha_height = {}
    """.format(x, y, width, height))

def main():
    print("🎯 Coordinate Recorder for Captcha Area")
    print("======================================")
    print()
    print("This tool will help you find the exact coordinates of the captcha image.")
    print("Steps:")
    print("1. Make sure your captcha dialog is visible on screen")
    print("2. Move your cursor to each corner of the captcha image")
    print("3. Press ENTER at each corner to record the position")
    print("4. Record all 4 corners in sequence: Top-Left, Top-Right, Bottom-Right, Bottom-Left")
    print()
    print("Additional Information Needed:")
    dialog_center_x = input("Enter the dialog center X coordinate (leave empty to skip): ")
    dialog_center_y = input("Enter the dialog center Y coordinate (leave empty to skip): ")
    
    # Store dialog center if provided
    global location
    if dialog_center_x and dialog_center_y:
        location = (int(dialog_center_x), int(dialog_center_y))
    else:
        location = (0, 0)
        print("Dialog center not provided. Some calculations will use (0,0) as reference.")
    
    print("\nPress any key to start recording...")
    keyboard.read_event()
    
    # Start recording coordinates
    coords = record_coordinates()
    
    # Generate code to update auto_clicker.py
    if coords:
        update_auto_clicker_code(coords)
    
    print("\nDone! Check the coord_screenshots folder for verification images.")
    print("Now you can update your auto_clicker.py with the correct coordinates.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nRecording stopped by user.")
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        print("\nExiting coordinate recorder.")
