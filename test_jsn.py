import json

data = {'results': [{'gender': 'male', 'name': {'title': 'Mr', 'first': '<PERSON>', 'last': '<PERSON>'}, 'location': {'street': {'number': 4486, 'name': 'Windsor Road'}, 'city': 'Manchester', 'state': 'Merseyside', 'country': 'United Kingdom', 'postcode': 'LZ4 7EQ', 'coordinates': {'latitude': '-5.5632', 'longitude': '-138.8094'}, 'timezone': {'offset': '-7:00', 'description': 'Mountain Time (US & Canada)'}}, 'email': '<EMAIL>', 'login': {'uuid': '1927f896-23ad-4384-8e60-190b28b0d5ce', 'username': 'heavybear177', 'password': 'vader1', 'salt': 'loP1nHpc', 'md5': 'edddc948c1183f279f370eb6d1b067da', 'sha1': '2a14cba2466e553f68a9a31d176c29071b793794', 'sha256': '0b71ec6861efa823c9b3783c887fb1d64e64f43a974a92111c715e7ddd164e75'}, 'dob': {'date': '1977-10-29T05:23:35.927Z', 'age': 47}, 'registered': {'date': '2002-12-18T17:05:37.392Z', 'age': 22}, 'phone': '0113495 945 1154', 'cell': '07866 730634', 'id': {'name': 'NINO', 'value': 'EE 46 03 82 G'}, 'picture': {'large': 'https://randomuser.me/api/portraits/men/73.jpg', 'medium': 'https://randomuser.me/api/portraits/med/men/73.jpg', 'thumbnail': 'https://randomuser.me/api/portraits/thumb/men/73.jpg'}, 'nat': 'GB'}], 'info': {'seed': '344da81ff210651f', 'results': 1, 'page': 1, 'version': '1.4'}}


print(data["results"][0]["gender"])