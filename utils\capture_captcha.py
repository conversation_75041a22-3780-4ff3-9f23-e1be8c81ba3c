#!/usr/bin/env python3
"""
Captcha Screenshot Capture Tool
Use this to capture proper screenshots of captcha dialogs for image detection.
"""

import pyautogui
import time
import os

def capture_captcha_dialog():
    """Interactive tool to capture captcha dialog screenshots."""
    
    print("🎯 Captcha Dialog Screenshot Tool")
    print("=" * 40)
    print()
    print("This tool will help you capture the perfect screenshot")
    print("of your captcha dialog for image detection.")
    print()
    print("INSTRUCTIONS:")
    print("1. Position your browser/app so the captcha dialog is visible")
    print("2. Make sure the entire captcha dialog is on screen")
    print("3. Don't move the mouse during capture")
    print()
    
    input("Press ENTER when you're ready to start... ")
    
    # Countdown
    for i in range(5, 0, -1):
        print(f"Capturing in {i} seconds...")
        time.sleep(1)
    
    print("📸 Capturing screenshot...")
    
    # Capture full screen
    screenshot = pyautogui.screenshot()
    
    # Save with timestamp
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"captcha_screenshot_{timestamp}.png"
    
    # Create screens directory if it doesn't exist
    if not os.path.exists("screens"):
        os.makedirs("screens")
    
    filepath = os.path.join("screens", filename)
    screenshot.save(filepath)
    
    print(f"✅ Screenshot saved: {filepath}")
    print()
    print("NEXT STEPS:")
    print(f"1. Open {filepath} in an image editor")
    print("2. Crop ONLY the captcha dialog part (not the entire screen)")
    print("3. Save the cropped image as 'screens/captcha_dialog.png'")
    print("4. The cropped image should show just the captcha dialog box")
    print()
    print("💡 TIPS:")
    print("- Make sure the dialog has clear edges")
    print("- Include the dialog border/frame")
    print("- Don't include background elements")
    print("- Keep the image crisp and clear")
    
    # Also show mouse position for reference
    mouse_pos = pyautogui.position()
    screen_size = pyautogui.size()
    print(f"\n📍 Reference Info:")
    print(f"   Mouse position when captured: {mouse_pos}")
    print(f"   Screen size: {screen_size}")

if __name__ == "__main__":
    capture_captcha_dialog()
