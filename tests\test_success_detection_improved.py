"""
Test script to verify the improved success message detection
"""

from detect_success_message_improved import detect_success_combined
import os
import time
import cv2
import numpy as np

def test_detection_with_sample():
    """Test the detection with a sample screenshot"""
    
    # List of screenshots to test with
    test_files = [
        "temp_settings_check.png", 
        "temp_settings_check_error.png",
        # Add more test files here if needed
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n\nTesting with {test_file}:")
            success = detect_success_combined(test_file)
            print(f"Detection result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            
            # Let's also visualize the image with some annotations
            img = cv2.imread(test_file)
            height, width = img.shape[:2]
            
            # Draw the typical regions where we'd expect UI elements
            debug_img = img.copy()
            
            # Success banner area (10-25% down)
            banner_top = int(height * 0.10)
            banner_bottom = int(height * 0.25)
            cv2.rectangle(debug_img, (0, banner_top), (width, banner_bottom), (0, 255, 0), 2)
            cv2.putText(debug_img, "Success Banner Detection Zone", (10, banner_top - 10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
            
            # GMX header area (0-5% down)
            header_bottom = int(height * 0.05)
            cv2.rectangle(debug_img, (0, 0), (width, header_bottom), (0, 0, 255), 2)
            cv2.putText(debug_img, "GMX Header Zone (Ignored)", (10, 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)
            
            # Save the annotated image
            base_name = os.path.splitext(test_file)[0]
            cv2.imwrite(f"debug_analysis/{base_name}_analyzed.png", debug_img)
            
        else:
            print(f"File not found: {test_file}")

if __name__ == "__main__":
    print("🧪 Testing Improved Success Message Detection")
    print("=" * 60)
    
    # Create debug directory
    os.makedirs("debug_analysis", exist_ok=True)
    
    # Test with sample files
    test_detection_with_sample()
    
    print("\n\n🔍 Now testing with live screen capture")
    print("Please ensure the success message is visible on screen")
    for i in range(3, 0, -1):
        print(f"Capturing in {i}...")
        time.sleep(1)
    
    # Test with live capture
    detect_success_combined()
