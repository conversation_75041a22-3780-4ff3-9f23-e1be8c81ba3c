#!/usr/bin/env python3
"""
Quick Captcha Detection Test
Run this to test the improved captcha detection with multiple confidence levels.
"""

from auto_clicker_bb import ImageClicker
import time

def test_improved_captcha_detection():
    """Test the new wait_for_captcha method with adaptive confidence levels."""
    
    print("🧪 Testing Improved Captcha Detection")
    print("=" * 50)
    
    # Create clicker with the new default confidence of 0.7
    clicker = ImageClicker(confidence=0.7, wait_time=2)
    
    print("This test will:")
    print("1. Take a current screenshot")
    print("2. Test the new wait_for_captcha method")
    print("3. Try multiple confidence levels automatically")
    print("4. Save debug screenshots if needed")
    print()
    
    # Take an immediate screenshot for reference
    print("📸 Taking reference screenshot...")
    clicker.capture_full_screen_debug("test_reference.png")
    
    # Test the new wait_for_captcha method
    print("\n🔍 Testing new wait_for_captcha method...")
    print("Make sure you have a captcha dialog visible on screen!")
    
    for i in range(5, 0, -1):
        print(f"Starting test in {i} seconds...")
        time.sleep(1)
    
    print("\n🚀 Testing captcha detection...")
    
    # Test with a shorter timeout for quick feedback
    location = clicker.wait_for_captcha("screens/captcha_dialog.png", timeout=15)
    
    if location:
        print(f"✅ SUCCESS! Captcha dialog detected at: {location}")
        print(f"📍 You can now use this location for captcha processing")
        
        # Demonstrate captcha area calculation
        print(f"\n📏 Captcha area suggestions based on detected location:")
        x, y = location
        print(f"   Option 1 (small): x={x-50}, y={y-25}, width=100, height=50")
        print(f"   Option 2 (medium): x={x-100}, y={y-50}, width=200, height=80") 
        print(f"   Option 3 (large): x={x-150}, y={y-75}, width=300, height=120")
        
        return True
    else:
        print(f"❌ Captcha dialog not detected")
        print(f"💡 Check the debug screenshots in 'debug_screenshots' folder")
        print(f"💡 Make sure 'screens/captcha_dialog.png' matches what's on screen")
        
        # Run comprehensive debugging
        print(f"\n🔧 Running comprehensive debugging...")
        clicker.debug_captcha_detection("screens/captcha_dialog.png")
        
        return False

def test_all_screen_images():
    """Test detection of all images in the screens folder."""
    
    print(f"\n🎯 Testing All Screen Images")
    print("=" * 30)
    
    clicker = ImageClicker(confidence=0.7)
    
    import os
    screens_dir = "screens"
    
    if not os.path.exists(screens_dir):
        print(f"❌ Screens directory not found: {screens_dir}")
        return
    
    image_files = [f for f in os.listdir(screens_dir) 
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    
    if not image_files:
        print(f"❌ No image files found in {screens_dir}")
        return
    
    print(f"Testing {len(image_files)} images...")
    found_count = 0
    
    for filename in image_files:
        image_path = os.path.join(screens_dir, filename)
        print(f"\n   🔍 Testing: {filename}")
        
        try:
            # Quick test with multiple confidence levels
            for conf in [0.7, 0.6, 0.8, 0.5]:
                result = clicker.analyze_screen_for_image(image_path, save_analysis=False)
                if result['matches_found'] > 0:
                    center = result['best_match']['center']
                    confidence = result['best_match']['confidence']
                    print(f"      ✅ FOUND at {center} (confidence: {confidence})")
                    found_count += 1
                    break
            else:
                print(f"      ❌ Not found")
        except Exception as e:
            print(f"      ⚠️ Error: {e}")
    
    print(f"\n📊 Summary: Found {found_count}/{len(image_files)} images on screen")
    return found_count

if __name__ == "__main__":
    print("🔧 Captcha Detection Improvement Test")
    print("=" * 60)
    
    # Test 1: Improved captcha detection
    success = test_improved_captcha_detection()
    
    # Test 2: Test all screen images
    found_images = test_all_screen_images()
    
    print(f"\n{'='*40} FINAL RESULTS {'='*40}")
    if success:
        print("✅ Captcha detection is working!")
        print("💡 You can now run your main automation script")
    else:
        print("❌ Captcha detection needs attention")
        print("💡 Check the debug screenshots and adjust your captcha_dialog.png")
    
    print(f"📊 Found {found_images} screen elements currently visible")
    print(f"📁 Debug screenshots saved in 'debug_screenshots' folder")
    print(f"\n🚀 Ready to proceed with automation!")
